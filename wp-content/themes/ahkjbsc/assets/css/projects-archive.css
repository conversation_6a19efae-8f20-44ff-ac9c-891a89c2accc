/* Projects Archive Page Styles */

.projects-archive {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 40px 0;
}

/* 页面头部 */
.page-header {
    text-align: left;
    margin-bottom: 30px;
    padding: 20px 0;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    position: relative;
    padding-left: 20px;
}

.page-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #1890ff;
    border-radius: 2px;
}

/* 内容区域 */
.projects-content {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 左侧项目列表 */
.projects-list-section {
    padding: 24px;
    border-right: 1px solid #f0f0f0;
}

.projects-table-wrapper {
    margin-bottom: 24px;
}

.projects-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.projects-table thead {
    background: #4a90c2;
    color: #fff;
}

.projects-table th {
    padding: 12px 16px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.projects-table th:last-child {
    border-right: none;
}

.sequence-col {
    width: 80px;
}

.company-col {
    width: auto;
}

.projects-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.projects-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.projects-table tbody tr:hover {
    background: #e6f7ff !important;
    transform: translateX(2px);
}

.projects-table td {
    padding: 12px 16px;
    text-align: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    border-right: 1px solid #f0f0f0;
}

.projects-table td:last-child {
    border-right: none;
}

.sequence-cell {
    font-weight: 600;
    color: #1890ff;
}

.company-cell {
    text-align: left;
}

.company-link {
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s;
    display: block;
    padding: 4px 0;
}

.company-link:hover {
    color: #1890ff;
    text-decoration: underline;
}

.no-projects {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    padding: 40px 16px;
}

/* 统计摘要 */
.projects-summary {
    margin-top: 20px;
}

.summary-box {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border: 2px solid #fdcb6e;
    border-radius: 8px;
    padding: 16px 20px;
    font-size: 15px;
    color: #2d3436;
    text-align: center;
    font-weight: 500;
    line-height: 1.6;
}

.highlight-number {
    color: #d63031;
    font-weight: 700;
    font-size: 18px;
    margin: 0 2px;
}

/* 右侧统计区域 */
.projects-stats-section {
    padding: 24px;
}

.stats-card {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 24px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.stats-header {
    background: #c0392b;
    color: #fff;
    padding: 12px 20px;
    text-align: center;
}

.stats-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
}

.chart-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
}

.stats-content {
    padding: 20px;
}

.stats-text {
    font-size: 14px;
    line-height: 1.8;
    color: rgba(0, 0, 0, 0.85);
    text-align: justify;
}

.stats-text strong {
    color: #1890ff;
    font-weight: 700;
    font-size: 16px;
}

/* 图表区域 */
.chart-card {
    background: #fff;
}

.chart-card .stats-header {
    background: #f39c12;
}

.chart-container {
    padding: 20px;
    text-align: center;
}

#categoryChart {
    max-width: 80%;
    margin-bottom: 20px;
    margin: 20px auto;
}

/* 图例 */
.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.legend-label {
    flex: 1;
    text-align: left;
    color: rgba(0, 0, 0, 0.85);
}

.legend-percentage {
    font-weight: 600;
    color: #1890ff;
    min-width: 35px;
    text-align: right;
}

.no-data,
.no-chart-data {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .projects-content .ant-row {
        flex-direction: column;
    }

    .projects-list-section {
        border-right: none;
        border-bottom: 1px solid #f0f0f0;
    }

    .projects-stats-section {
        padding-top: 0;
    }
}

@media (max-width: 768px) {
    .projects-archive {
        padding: 20px 0;
    }

    .page-title {
        font-size: 24px;
    }

    .projects-list-section,
    .projects-stats-section {
        padding: 16px;
    }

    .projects-table {
        font-size: 13px;
    }

    .projects-table th,
    .projects-table td {
        padding: 8px 12px;
    }

    .sequence-col {
        width: 60px;
    }

    .summary-box {
        font-size: 14px;
        padding: 12px 16px;
    }

    .highlight-number {
        font-size: 16px;
    }

    .stats-text {
        font-size: 13px;
    }

    .chart-container {
        padding: 16px;
    }

    #categoryChart {
        height: 180px !important;
    }
}

@media (max-width: 480px) {
    .projects-archive {
        padding: 16px 0;
    }

    .page-header {
        margin-bottom: 20px;
        padding: 16px 0;
    }

    .page-title {
        font-size: 20px;
        padding-left: 16px;
    }

    .projects-list-section,
    .projects-stats-section {
        padding: 12px;
    }

    .projects-table {
        font-size: 12px;
    }

    .projects-table th,
    .projects-table td {
        padding: 6px 8px;
    }

    .company-link {
        font-size: 13px;
    }

    .summary-box {
        font-size: 13px;
        padding: 10px 12px;
    }

    .stats-content {
        padding: 16px;
    }

    .chart-container {
        padding: 12px;
    }

    #categoryChart {
        height: 160px !important;
    }

    .legend-item {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.projects-table tbody tr {
    animation: fadeInUp 0.3s ease-out;
}

.projects-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.projects-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.projects-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.projects-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.projects-table tbody tr:nth-child(5) { animation-delay: 0.5s; }

.stats-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.2s; }
.stats-card:nth-child(2) { animation-delay: 0.4s; }

/* 数字计数动画 */
.highlight-number {
    display: inline-block;
    transition: all 0.3s ease;
}

.highlight-number:hover {
    transform: scale(1.1);
}

/* 图表加载动画 */
.chart-container {
    position: relative;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 图表加载完成后隐藏加载动画 */
.chart-loaded .chart-container::before {
    display: none;
}
