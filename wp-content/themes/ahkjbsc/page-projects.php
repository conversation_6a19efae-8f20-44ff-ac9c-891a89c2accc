<?php
/*
Template Name: 统计页面模板
Template Post Type: page
 */
get_header();
?>

<div class="projects-archive">
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">项目概述</h1>
        </div>
        <div class="projects-content">
            <div class="ant-row">
                <!-- 左侧项目列表 -->
                <div class="ant-col ant-col-14">
                    <div class="projects-list-section">
                        <div class="projects-table-wrapper">
                            <table class="projects-table">
                                <thead>
                                    <tr>
                                        <th class="sequence-col">序号</th>
                                        <th class="company-col">企业名称</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // 获取项目列表
                                    $projects_query = new WP_Query(array(
                                        'post_type' => 'projects',
                                        'numberposts' => -1,
                                        'post_status' => 'publish',
                                        'orderby' => 'date',
                                        'order' => 'DESC'
                                    ));
                                    
                                    if ($projects_query->have_posts()) :
                                        $sequence = 1;
                                        while ($projects_query->have_posts()) : $projects_query->the_post();
                                    ?>
                                    <tr class="project-row">
                                        <td class="sequence-cell"><?php echo $sequence; ?></td>
                                        <td class="company-cell">
                                            <a href="<?php the_permalink(); ?>" class="company-link">
                                                <?php the_title(); ?>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php 
                                        $sequence++;
                                        endwhile;
                                        wp_reset_postdata();
                                    else :
                                    ?>
                                    <tr>
                                        <td colspan="2" class="no-projects">暂无项目数据</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 统计信息 -->
                        <?php
                        $total_projects = wp_count_posts('projects')->publish;
                        $featured_projects = get_posts(array(
                            'post_type' => 'projects',
                            'meta_key' => 'is_featured',
                            'meta_value' => '1',
                            'post_status' => 'publish',
                            'numberposts' => -1
                        ));
                        $featured_count = count($featured_projects);
                        ?>
                        
                        <div class="projects-summary">
                            <div class="summary-box">
                                目前入驻<span class="highlight-number"><?php echo $total_projects; ?></span>家企业，
                                其中<span class="highlight-number"><?php echo $featured_count; ?></span>家为新注册公司。
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧统计信息 -->
                <div class="ant-col ant-col-10">
                    <div class="projects-stats-section">
                        <!-- 企业数量统计 -->
                        <div class="stats-card">
                            <div class="stats-header">
                                <h3 class="stats-title">在孵企业数量</h3>
                            </div>
                            <div class="stats-content">
                                <?php
                                // 获取项目分类统计
                                $project_categories = get_terms(array(
                                    'taxonomy' => 'project_category',
                                    'hide_empty' => true,
                                    'orderby' => 'count',
                                    'order' => 'DESC'
                                ));
                                
                                $total_count = 0;
                                $category_data = array();
                                $max = array();
                                
                                if ($project_categories && !is_wp_error($project_categories)) {
                                    foreach ($project_categories as $category) {
                                        $total_count += $category->count;
                                        $category_data[] = array(
                                            'name' => $category->name,
                                            'count' => $category->count,
                                            'percentage' => 0 // 稍后计算
                                        );
                                        if ($category->count > $max['count']){
                                            $max = array(
                                                'name' => $category->name,
                                                'count' => $category->count,
                                                'percentage' => 0 // 稍后计算
                                            );
                                        }
                                    }
                                    $max['percentage'] = $max['count'] / $total_count * 100;
                                    // 计算百分比
                                    foreach ($category_data as &$data) {
                                        $data['percentage'] = $total_count > 0 ? round(($data['count'] / $total_count) * 100) : 0;
                                    }
                                }
                                ?>
                                
                                <div class="stats-text">
                                    自2024年7月基地众创运营以来，共孵化<strong><?php echo $total_count; ?></strong>家企业（协议首年度运营指标为引进8个项目），包括人工智能、生物医药等产业领域，其中人工智能领域企业占84%。
                                </div>
                            </div>
                        </div>
                        
                        <!-- 饼图统计 -->
                        <div class="stats-card chart-card">
                            <div class="stats-header">
                                <h4 class="chart-title">入驻企业产业领域</h4>
                            </div>
                            <div class="chart-container">
                                <canvas id="categoryChart" width="300" height="200"></canvas>
                                
                                <!-- 图例 -->
                                <div class="chart-legend">
                                    <?php if (!empty($category_data)) : ?>
                                        <?php foreach ($category_data as $index => $data) : ?>
                                        <div class="legend-item">
                                            <span class="legend-color" data-color-index="<?php echo $index; ?>"></span>
                                            <span class="legend-label"><?php echo esc_html($data['name']); ?></span>
                                            <span class="legend-percentage"><?php echo $data['percentage']; ?>%</span>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else : ?>
                                        <div class="no-data">暂无分类数据</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js 库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// 饼图数据
const categoryData = <?php echo json_encode($category_data); ?>;

// 颜色配置
const colors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色  
    '#faad14', // 橙色
    '#f5222d', // 红色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#eb2f96', // 粉色
    '#fa8c16'  // 橙红色
];

// 创建饼图
if (categoryData && categoryData.length > 0) {
    const ctx = document.getElementById('categoryChart').getContext('2d');
    
    const chartData = {
        labels: categoryData.map(item => item.name),
        datasets: [{
            data: categoryData.map(item => item.count),
            backgroundColor: colors.slice(0, categoryData.length),
            borderColor: '#fff',
            borderWidth: 2,
            hoverBorderWidth: 3
        }]
    };
    
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    display: false // 使用自定义图例
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value}家 (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1000
            }
        }
    });
    
    // 设置图例颜色
    document.querySelectorAll('.legend-color').forEach((element, index) => {
        element.style.backgroundColor = colors[index] || '#ccc';
    });
    //chart-loaded
    document.querySelector('.chart-card').classList.add('chart-loaded');


} else {
    // 如果没有数据，显示空状态
    document.getElementById('categoryChart').style.display = 'none';
    document.querySelector('.chart-container').innerHTML = '<div class="no-chart-data">暂无图表数据</div>';
}

// 表格行悬停效果
document.querySelectorAll('.project-row').forEach(row => {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f0f8ff';
    });
    
    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});

// 数字动画效果
function animateNumbers() {
    const numbers = document.querySelectorAll('.highlight-number');
    numbers.forEach(number => {
        const target = parseInt(number.textContent);
        let current = 0;
        const increment = target / 30;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            number.textContent = Math.floor(current);
        }, 50);
    });
}

// 页面加载完成后执行动画
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(animateNumbers, 500);
});
</script>

<?php
get_footer();
?>
