<?php
$allmeta = acf_get_value('options_slider_list');
$meta = array();
if ( $allmeta ) {
    foreach ( $allmeta as $key => $value ) {

        // If a reference exists for this value, add it to the meta array.
        if ( isset( $allmeta[ "_$key" ] ) ) {
            $meta[ $key ]    = $allmeta[ $key ][0];
            $meta[ "_$key" ] = $allmeta[ "_$key" ][0];
        }
    }
}

// Unserialized results (get_metadata does not unserialize if $key is empty).
$meta = array_map( 'acf_maybe_unserialize', $meta );

$ob = acf_get_field('options_slider_list');
dd($meta, $ob);
?>
<div id="slideBox" class="slideBox">
    <div class="hd">
        <ul>
            <li class="on">1</li>
            <li class="">2</li>
            <li class="">2</li>
            <li class="">2</li>
        </ul>
    </div>
    <div class="bd">
        <div class="tempWrap">
            <ul>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
                <li>
                    <a href="http://www.superslide2.com/" target="_blank">
                        <img src="<?php theme_img('1.png'); ?>">
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>